import datetime
import json
import logging
from typing import Op<PERSON>, Union, List
from sqlalchemy import <PERSON><PERSON><PERSON>, and_, or_

from flask import g
from app import app

from clientmodels import EntitlementGroup, EntitlementGroupUserAssignment, Notification, UserAssignment, UserNotification, UserNotificationLog, User, UserToken, Xircle, XircleMember, \
    Feed, FeedComment, Client
from services.azure_queue import AzureQueue
from services.google_fcm import GoogleFCMService
from services.email_service import EmailService

logger = logging.getLogger(__name__)

class NotificationFunction:
    def __init__(self):
        self.queue = AzureQueue()
        if app.config.get('ENVIRONMENT') == 'dev':
            self.queue_name = "notification-queue-dev"
        else:
            self.queue_name = "notification-queue"

    def async_create_notification(
        self,
        client_id: str,
        recipient_type: str,
        recipient_ids: Union[List[str], str],
        title: str,
        body: str,
        data: dict,
        notification_type: str,
        enable_notification_push: bool = False,
        enable_email_notification: bool = False,
        update_badge: bool = False,
        scheduled_for: Optional[datetime.datetime] = None,
    ) -> Optional[Notification]:
        """Create a notification and queue it for delivery
        
        Args:
            recipient_type: Recipient type, one of "all", "entitlement_group", "individual"
            recipient_ids: List of user IDs or "all" to send to all users
            title: Notification title
            body: Notification body
            notification_type: Type of notification
            scheduled_for: When to send the notification
            update_badge: Whether to update the badge count
        """
        try:
            # Create notification record
            notification = Notification()

            notification.client_id=client_id
            notification.recipient_type=recipient_type
            notification.recipient_ids=recipient_ids
            notification.title=title
            notification.body=body
            notification.data=data
            notification.notification_type=notification_type
            notification.update_badge=update_badge
            notification.enable_notification_push=enable_notification_push
            notification.enable_email_notification=enable_email_notification
            notification.status='pending'

            g.db_session.add(notification)
            g.db_session.flush()

            notification.data = notification.data | {"id": notification.id}

            if scheduled_for:
                # Validate scheduled time
                current_time = datetime.datetime.utcnow()
                if scheduled_for < current_time:
                    raise ValueError("Cannot schedule notification in the past")
                notification.scheduled_for=scheduled_for

            else:
                # Queue the notification for processing
                message = {
                    "notification_id": notification.id,
                }
                
                enqueued_message = self.queue.send_message(
                    json.dumps(message), 
                    self.queue_name,
                    visibility_timeout=0
                )
            
                notification.queue_id = enqueued_message.id

            g.db_session.commit()

            return notification

        except Exception as e:
            logger.error(f"Error creating notification: {str(e)}")
            return None

    def create_notification(
        self,
        client_id: str,
        recipient_type: str,
        recipient_ids: Union[List[str], str],
        title: str,
        body: str,
        data: dict,
        notification_type: str,
        enable_notification_push: bool = False,
        enable_email_notification: bool = False,
        update_badge: bool = False,
        scheduled_for: Optional[datetime.datetime] = None,
    ) -> Optional[Notification]:
        """Create a notification and send it immediately if scheduled_for is not provided
        
        Args:
            recipient_type: Recipient type, one of "all", "entitlement_group", "individual"
            recipient_ids: List of user IDs or "all" to send to all users
            title: Notification title
            body: Notification body
            notification_type: Type of notification
            scheduled_for: When to send the notification
            update_badge: Whether to update the badge count
        """
        try:
            # Create notification record
            notification = Notification()

            notification.client_id=client_id
            notification.recipient_type=recipient_type
            notification.recipient_ids=recipient_ids
            notification.title=title
            notification.body=body
            notification.data=data
            notification.notification_type=notification_type
            notification.update_badge=update_badge
            notification.enable_notification_push=enable_notification_push
            notification.enable_email_notification=enable_email_notification
            notification.status='pending'

            g.db_session.add(notification)
            g.db_session.flush()

            notification.data = notification.data | {"id": notification.id}

            if scheduled_for:
                # Validate scheduled time
                current_time = datetime.datetime.utcnow()
                if scheduled_for < current_time:
                    raise ValueError("Cannot schedule notification in the past")
                notification.scheduled_for=scheduled_for

            else:
                self.send_notification(notification.id)

            g.db_session.commit()

            return notification

        except Exception as e:
            logger.error(f"Error creating notification: {str(e)}")
            return None

    def update_notification(
        self,
        notification_id: str,
        recipient_type: Optional[str] = None,
        recipient_ids: Optional[Union[List[str], str]] = None,
        title: Optional[str] = None,
        body: Optional[str] = None,
        data: Optional[dict] = None,
        notification_type: Optional[str] = None,
        enable_notification_push: Optional[bool] = None,
        enable_email_notification: Optional[bool] = None,
        update_badge: Optional[bool] = False,
        scheduled_for: Optional[datetime.datetime] = None,
    ) -> bool:
        """Update a queued notification"""
        try:
            # Get notification
            notification = g.db_session.query(Notification).get(notification_id)
            
            # Update notification fields
            if recipient_type:
                notification.recipient_type = recipient_type
            if recipient_ids:
                notification.recipient_ids = recipient_ids
            if title:
                notification.title = title
            if body:
                notification.body = body
            if data:
                notification.data = data
            if notification_type:
                notification.notification_type = notification_type
            if enable_notification_push:
                notification.enable_notification_push = enable_notification_push
            if enable_email_notification:
                notification.enable_email_notification = enable_email_notification
            if update_badge:
                notification.update_badge = update_badge

            # Commit changes to ensure we have latest data
            g.db_session.commit()

            try:
                # Update the queue
                if scheduled_for and scheduled_for != notification.scheduled_for:
                    # Validate scheduled time
                    current_time = datetime.datetime.utcnow()
                    if scheduled_for < current_time:
                        raise ValueError("Cannot schedule notification in the past")

                    notification.scheduled_for = scheduled_for
                
                # Commit queue changes
                g.db_session.commit()
                return True

            except Exception as e:
                # Rollback only queue changes if they fail
                g.db_session.rollback()
                logger.error(f"Error updating notification queue: {str(e)}")
                return False

        except Exception as e:
            # Rollback all changes if notification update fails
            g.db_session.rollback()
            logger.error(f"Error updating notification: {str(e)}")
            return False

    def delete_notification(
        self,
        notification_id: str
    ) -> bool:
        """Delete a notification and its user notifications"""
        try:
            notification = g.db_session.query(Notification).filter(Notification.id == notification_id).first()
            if not notification:
                return False
            
            # Delete queue message
            notification.queue_id = None

            # Delete user notifications
            notification.delete()
            g.db_session.commit()
            return True

        except Exception as e:
            logger.error(f"Error deleting notification: {str(e)}")
            return False

    def send_notification(
        self,
        notification_id: str
    ) -> bool:
        # Send notification to users        
        # Get notification from database
        try:
            notification = g.db_session.query(Notification).filter(
                Notification.id == notification_id, 
                Notification.is_deleted == False
            ).first()

            if notification is None:
                return False
            
            tenant_id = g.db_session.query(Client).filter(
                Client.id == notification.client_id
            ).first().id_key

            if tenant_id is None:
                return False       
                
            # Send via FCM
            google_fcm = GoogleFCMService()
            email_service = EmailService()

            # Get target users
            target_user_ids = []

            if notification.recipient_type == 'all':
                # Send to all users
                if notification.client_id == "global":
                    users = g.db_session.query(User).all()
                else:
                    users = g.db_session.query(User).join(
                        UserAssignment, User.id == UserAssignment.user_id
                    ).filter(
                        UserAssignment.client_id == notification.client_id
                    ).all()
                target_user_ids = [user.id for user in users]
            elif notification.recipient_type == 'entitlement_group':
                # Get the entitlement group
                if not notification.recipient_ids:
                    logger.error("No recipient IDs provided for entitlement group notification")
                    return False
                
                # Query the entitlement group
                entitlement_groups = g.client_db_session.query(EntitlementGroup).filter(
                    EntitlementGroup.id.in_(notification.recipient_ids),
                    EntitlementGroup.is_deleted == False
                ).all()
                
                if not entitlement_groups:
                    logger.error(f"Entitlement group not found: {notification.recipient_ids}")
                    return False
                
                # Get users based on directory mapping or direct assignments
                target_user_ids = set()  # Use set to avoid duplicates

                # Split entitlement groups by type
                directory_groups = []
                assignment_groups = []
                for group in entitlement_groups:
                    if group.directory_mapping_key and group.directory_mapping_value:
                        directory_groups.append((group.directory_mapping_key, group.directory_mapping_value))
                    assignment_groups.append(group.id)

                # Handle directory mapping groups in a single query if possible
                if directory_groups:
                    conditions = []
                    for mapping_key, mapping_value in directory_groups:
                        conditions.append(
                            and_(
                                getattr(User, mapping_key) == mapping_value,
                                User.is_deleted == False
                            )
                        )
                    if conditions:
                        user_ids = g.db_session.query(User.id).filter(or_(*conditions)).all()
                        target_user_ids.update(uid for (uid,) in user_ids)

                # Handle direct assignment groups in a single query
                if assignment_groups:
                    user_ids = g.db_session.query(User.id).join(
                        EntitlementGroupUserAssignment,
                        EntitlementGroupUserAssignment.user_id == User.id
                    ).filter(
                        EntitlementGroupUserAssignment.entitlement_group_id.in_(assignment_groups),
                        EntitlementGroupUserAssignment.is_deleted == False,
                        User.is_deleted == False
                    ).distinct().all()
                    target_user_ids.update(uid for (uid,) in user_ids)

                if not target_user_ids:
                    logger.warning(f"No users found in entitlement groups: {notification.recipient_ids}")
                
                # Convert set back to list for consistency with the rest of the code
                target_user_ids = list(target_user_ids)
            elif notification.recipient_type == 'individual':
                # Send to specific users
                target_user_ids = notification.recipient_ids

            success_count = 0
            failure_count = 0

            if not target_user_ids:
                logger.error("No target users found for notification")
                return False

            # Send to specific users
            for target_user_id in target_user_ids:

                # Get user object for email sending
                user = g.db_session.query(User).filter(User.id == target_user_id).first()
                if not user:
                    logger.error(f"User not found: {target_user_id}")
                    continue

                # Get user's notification preferences
                user_settings = user.settings or {}
                notification_preferences = user_settings.get('notification_preference', {})
                
                # Check if user has enabled 'all' notifications for push
                user_wants_push = notification_preferences.get('all', True)  # Default to True if not set
                
                # Check if user has enabled 'email_all' notifications for email
                user_wants_email = notification_preferences.get('email_all', True)  # Default to True if not set

                # Get user's device tokens
                user_tokens = g.db_session.query(UserToken).filter(
                    UserToken.user_id == user.id,
                    UserToken.device_token.is_not(None),
                    UserToken.device_type.in_(['android', 'ios', 'web'])
                ).all()
                
                user_notification = UserNotification()
                user_notification.notification_id = notification.id
                user_notification.user_id = user.id
                user_notification.status = 'pending'
                g.db_session.add(user_notification)
                g.db_session.flush()  # Flush to generate the ID for user_notification

                # Send push notification - respect both notification flag and user preference
                if notification.enable_notification_push and user_wants_push and len(user_tokens) > 0:
                    try:
                        for user_token in user_tokens:
                            user_notification_log = UserNotificationLog()
                            user_notification_log.user_notification_id = user_notification.id
                            user_notification_log.user_id = user.id
                            user_notification_log.device_token = user_token.device_token
                            user_notification_log.device_type = user_token.device_type
                            user_notification_log.device_id = user_token.device_id
                            user_notification_log.status = 'pending'
                            g.db_session.add(user_notification_log)
                            
                            if notification.update_badge:
                                badge = int(self.get_unread_count(target_user_id))+1
                            else:
                                badge = None

                            # Send FCM notification
                            success = google_fcm.push_notification(
                                token=user_token.device_token,
                                title=notification.title,
                                body=notification.body,
                                data=notification.data,
                                badge=badge,
                                device_type=user_token.device_type
                            )

                            if success:
                                user_notification_log.status = 'sent'
                                success_count += 1
                            else:
                                user_notification_log.status = 'failed'
                                user_notification_log.error_message = 'FCM delivery failed'
                                failure_count += 1
                            
                    except Exception as e:
                        user_notification_log.status = 'failed'
                        user_notification_log.error_message = str(e)
                        logger.error(f'Error sending push notification to user {user.id}: {str(e)}')
                        failure_count += 1

                # Send email notification - respect both notification flag and user preference
                if notification.enable_email_notification and user_wants_email and user.email:
                    try:
                        # Create email notification log entry
                        email_notification_log = UserNotificationLog()
                        email_notification_log.user_notification_id = user_notification.id
                        email_notification_log.user_id = user.id
                        email_notification_log.device_token = user.email  # Reuse device_token field for email
                        email_notification_log.device_type = 'email'
                        email_notification_log.device_id = f'email_{user.id}'
                        email_notification_log.status = 'pending'
                        g.db_session.add(email_notification_log)
                        
                        email_result = email_service.send_notification_email(
                            email=user.email,
                            first_name=user.first_name,
                            subject=notification.title,
                            content=notification.body,
                            data=notification.data,
                        )
                        
                        if email_result:
                            email_notification_log.status = 'sent'
                            success_count += 1
                        else:
                            email_notification_log.status = 'failed'
                            email_notification_log.error_message = 'Email delivery failed'
                            failure_count += 1
                            
                    except Exception as e:
                        email_notification_log.status = 'failed'
                        email_notification_log.error_message = str(e)
                        logger.error(f'Error sending email notification to user {user.id}: {str(e)}')
                        failure_count += 1
                    
                user_notification.status = 'sent'
            
            notification.status = 'sent'    
            g.db_session.commit()
            return True

        except Exception as e:
            logger.error(f"Error sending notification: {str(e)}")
            return False

    def get_unread_count(
        self, 
        user_id: str
    ) -> int:
        """Get the number of unread notifications for a user.
        
        Args:
            user_id: The ID of the user
            device_id: Optional device ID to get count for specific device
            
        Returns:
            int: Number of unread notifications
        """
        try:
            query = g.db_session.query(UserNotification).join(Notification, Notification.id == UserNotification.notification_id).filter(
                UserNotification.user_id == user_id,
                UserNotification.status == 'sent',
                UserNotification.is_badge_read == False,
                Notification.update_badge == True,
                Notification.is_deleted == False
            )
                
            return query.count()
        except Exception as e:
            logger.error(f"Error getting unread count: {str(e)}")
            return 0

    def update_badge_count(
        self, 
        user_id: str
    ) -> bool:
        """Update badge count for user's device(s)"""
        try:
            # Get user's device tokens
            query = g.db_session.query(UserToken).filter(
                UserToken.user_id == user_id,
                UserToken.device_token.isnot(None),
                UserToken.device_type.in_(['android', 'ios', 'web'])
            )
                
            user_tokens = query.all()
            
            # Get unread count
            unread_count = self.get_unread_count(user_id)
            
            # Send badge update to each device
            fcm = GoogleFCMService()
            for token in user_tokens:
                fcm.push_notification(
                    token=token.device_token,
                    title="",  # Empty title for silent notification
                    body="",   # Empty body for silent notification
                    data={
                        'badge_update': 'true',
                        'unread_count': str(unread_count)
                    },
                    badge=unread_count,
                    device_type=token.device_type
                )
            return True
            
        except Exception as e:
            logger.error(f"Error updating badge count: {str(e)}")
            return False

    def new_xircle_notification(
        self,
        xircle_id: str,
        user_id: str,
        invited_member_ids: List[str]
    ) -> bool:
        """Create a notification for xircle-related events."""
        try:
            # Get xircle info
            xircle = g.client_db_session.query(Xircle).filter(
                Xircle.id == xircle_id,
                Xircle.is_deleted == False
            ).first()

            if not xircle:
                return False

            # Get user info
            user = g.db_session.query(User).filter(
                User.id == user_id,
                User.is_deleted == False
            ).first()

            if not user:
                return False

            # Get client info
            client = g.db_session.query(Client).filter(
                Client.id_key == g.tenant_id
            ).first()

            if not client:
                return False

            # Create notification
            update_badge = True
            enable_notification_push = True
            enable_email_notification = True
            notification_type = 'added_to_xircle'
            title = f"Welcome to {xircle.name} Xircle"

            invitee_name = user.preferred_name if user.preferred_name else f"{user.first_name} {user.last_name}"
            
            body = f"You've been invited to {xircle.name} Xircle by {invitee_name}"
            data = {
                'type': 'xircle',
                'action': f'/community?xircle_id={xircle_id}&client_id={client.id}#feed',
            }

            # Send notifications to all invited members which has enable the new xircle notification
            users = g.db_session.query(User).filter(
                User.id.in_(invited_member_ids),
                (
                    (User.settings.is_(None)) |  # No settings at all
                    (User.settings['notification_preference'].is_(None)) |  # No notification preferences
                    (User.settings['notification_preference']['added_to_xircle'].is_(None)) |  # Setting not specified
                    (User.settings['notification_preference']['added_to_xircle'].cast(Boolean).is_(True))  # Explicitly enabled
                )
            ).all()
            invited_member_ids = [user.id for user in users]

            # Self create notification
            self.async_create_notification(
                client_id=client.id,
                recipient_type='individual',
                recipient_ids=invited_member_ids,
                title=title,
                body=body,
                data=data,
                notification_type=notification_type,
                enable_notification_push=enable_notification_push,
                enable_email_notification=enable_email_notification,
                update_badge=update_badge,
                scheduled_for=None
            )

            return True

        except Exception as e:
            logger.error(f"Error creating xircle notification: {str(e)}")
            g.db_session.rollback()
            return False

    def new_feed_notification(
        self,
        xircle_id: str,
        feed_id: str,
        user_id: str
    ) -> bool:
        """Create a notification for feed-related events."""
        try:
            # Get xircle name
            xircle = g.client_db_session.query(Xircle).filter(Xircle.id == xircle_id).first()
            user = g.db_session.query(User).filter(User.id == user_id).first()
            
            if not xircle or not user:
                return False

            xircle.date_updated = datetime.datetime.utcnow()
            g.client_db_session.add(xircle)
            g.client_db_session.commit()

            # Get client info
            client = g.db_session.query(Client).filter(
                Client.id_key == g.tenant_id
            ).first()

            if not client:
                return False

            update_badge = True
            enable_notification_push = True
            enable_email_notification = True
            notification_type = 'new_feed_on_xircle'
            title = f"New Post in {xircle.name}"
            body = f"{user.preferred_name or (user.first_name + ' ' + user.last_name)} posted in {xircle.name}"
            data = {
                'type': 'feed',
                'action': f'/community?xircle_id={xircle_id}&client_id={client.id}#feed',
            }

            # Get all xircle members except the poster
            xircle_member_ids = [member.user_id for member in g.client_db_session.query(XircleMember).filter(
                XircleMember.xircle_id == xircle_id,
                XircleMember.user_id != user_id
            ).all()]

            # Send notification to all members which has enable the new feed on xircle notification
            # Send notification to all members which has enable the new feed on xircle notification
            users = g.db_session.query(User).filter(
                User.id.in_(xircle_member_ids),
                (
                    (User.settings.is_(None)) |  # No settings at all
                    (User.settings['notification_preference'].is_(None)) |  # No notification preferences
                    (User.settings['notification_preference']['new_feed_on_xircle'].is_(None)) |  # Setting not specified
                    (User.settings['notification_preference']['new_feed_on_xircle'].cast(Boolean).is_(True))  # Explicitly enabled
                )
            ).all()
            xircle_member_ids = [user.id for user in users]
            
            # Self create notification
            self.async_create_notification(
                client_id=client.id, 
                recipient_type='individual',
                recipient_ids=xircle_member_ids, 
                title=title, 
                body=body, 
                data=data, 
                notification_type=notification_type, 
                enable_notification_push=enable_notification_push,
                enable_email_notification=enable_email_notification,
                update_badge=update_badge,
                scheduled_for=None
            )

            return True

        except Exception as e:
            logger.error(f"Error creating new feed notification: {str(e)}")
            g.db_session.rollback()
            return False

    def new_feed_comment_notification(
        self,
        feed_id: str,
        commenter_id: str
    ) -> bool:
        """Create a notification for feed comment-related events."""
        try:
            # Get feed and commenter details
            feed = g.client_db_session.query(Feed).filter(Feed.id == feed_id).first()
            commenter = g.db_session.query(User).filter(User.id == commenter_id).first()
            
            if not feed or not commenter:
                return False

            if feed.user_id == commenter_id:
                return False

            # Get client info
            client = g.db_session.query(Client).filter(
                Client.id_key == g.tenant_id
            ).first()

            if not client:
                return False

            # Create notification
            update_badge = True
            enable_notification_push = True
            enable_email_notification = True
            notification_type = 'new_comment_on_feed'
            title = f"New Comment on Your Post"
            body = f"{commenter.preferred_name or (commenter.first_name + ' ' + commenter.last_name)} commented on your post"
            data = {
                'type': 'feed',
                'action': f'/community?feed_id={feed_id}&client_id={client.id}#feed',
            }

            user_settings = g.db_session.query(User).filter(User.id == feed.user_id).first().settings or {}
            if user_settings.get('notification_preference', {}).get('new_comment_on_feed', True) == False:
                return False

            # Only notify the feed creator
            feed_creator_ids = [feed.user_id]

            # Self create notification
            self.async_create_notification(
                client_id=client.id, 
                recipient_type='individual',
                recipient_ids=feed_creator_ids, 
                title=title, 
                body=body, 
                data=data, 
                notification_type=notification_type, 
                enable_notification_push=enable_notification_push,
                enable_email_notification=enable_email_notification,
                update_badge=update_badge,
                scheduled_for=None
            )

            return True

        except Exception as e:
            logger.error(f"Error creating feed comment notification: {str(e)}")
            g.db_session.rollback()
            return False

    def new_comment_thread_notification(
        self,
        feed_id: str,
        commenter_id: str
    ) -> bool:
        """Create a notification for users who have previously commented on the feed post."""
        try:
            # Get feed and commenter details
            feed = g.client_db_session.query(Feed).filter(Feed.id == feed_id).first()
            commenter = g.db_session.query(User).filter(User.id == commenter_id).first()
            
            if not feed or not commenter:
                return False

            # Get all users who have previously commented on this feed
            # Exclude the current commenter and the original post author
            previous_commenters = g.client_db_session.query(FeedComment.user_id).filter(
                FeedComment.feed_id == feed_id,
                FeedComment.is_deleted == False,
                FeedComment.user_id != commenter_id,  # Exclude current commenter
                FeedComment.user_id != feed.user_id   # Exclude post author
            ).distinct().all()
            
            if not previous_commenters:
                return True  # No previous commenters to notify
            
            previous_commenter_ids = [user_id for (user_id,) in previous_commenters]
            
            # Get client info
            client = g.db_session.query(Client).filter(
                Client.id_key == g.tenant_id
            ).first()

            if not client:
                return False

            # Filter users based on notification preferences and validate they exist
            valid_user = g.db_session.query(User).filter(
                User.id.in_(previous_commenter_ids),
                (
                    (User.settings.is_(None)) |  # No settings at all
                    (User.settings['notification_preference'].is_(None)) |  # No notification preferences
                    (User.settings['notification_preference']['new_comment_on_feed'].is_(None)) |  # Setting not specified
                    (User.settings['notification_preference']['new_comment_on_feed'].cast(Boolean).is_(True))  # Explicitly enabled
                )
            ).all()

            valid_user_ids = [user.id for user in valid_user]
            
            if not valid_user_ids:
                return True  # No users with enabled preferences to notify

            # Create notification
            update_badge = True
            enable_notification_push = True
            enable_email_notification = True
            notification_type = 'new_comment_on_feed'
            title = "New Comment on a Post You Commented On"
            body = f"{commenter.preferred_name or (commenter.first_name + ' ' + commenter.last_name)} also commented on a post you commented on"
            data = {
                'type': 'feed',
                'action': f'/community?feed_id={feed_id}&client_id={client.id}#feed',
            }

            # Send notification to all valid previous commenters
            self.async_create_notification(
                client_id=client.id, 
                recipient_type='individual',
                recipient_ids=valid_user_ids, 
                title=title, 
                body=body, 
                data=data, 
                notification_type=notification_type, 
                enable_notification_push=enable_notification_push,
                enable_email_notification=enable_email_notification,
                update_badge=update_badge,
                scheduled_for=None
            )

            return True

        except Exception as e:
            logger.error(f"Error creating comment thread notification: {str(e)}")
            g.db_session.rollback()
            return False

    def new_mention_notification(
        self,
        feed_id: str,
        commenter_id: str,
        mentioned_user_ids: list
    ) -> bool:
        """Create a notification for users mentioned in feed comments."""
        try:
            if not mentioned_user_ids:
                return True
                
            # Get commenter details
            commenter = g.db_session.query(User).filter(
                User.id == commenter_id,
                User.is_deleted == False
            ).first()
            
            if not commenter:
                return False

            # Get client info
            client = g.db_session.query(Client).filter(
                Client.id_key == g.tenant_id
            ).first()

            if not client:
                return False

            # Filter out invalid user IDs and the commenter themselves
            valid_mentioned_users = g.db_session.query(User).filter(
                User.id.in_(mentioned_user_ids),
                User.id != commenter_id,
                (
                    (User.settings.is_(None)) |  # No settings at all
                    (User.settings['notification_preference'].is_(None)) |  # No notification preferences
                    (User.settings['notification_preference']['metion_on_comment'].is_(None)) |  # Setting not specified
                    (User.settings['notification_preference']['metion_on_comment'].cast(Boolean).is_(True))  # Explicitly enabled
                )
            ).all()
            
            valid_user_ids = [user.id for user in valid_mentioned_users]
            
            if not valid_user_ids:
                return True

            # Create notification data
            update_badge = True
            enable_notification_push = True
            enable_email_notification = True
            notification_type = 'metion_on_comment'
            title = "You were mentioned in a comment"
            body = f"{commenter.preferred_name or (commenter.first_name + ' ' + commenter.last_name)} mentioned you in a comment"
            data = {
                'type': 'feed',
                'action': f'/community?feed_id={feed_id}&client_id={client.id}#feed',
            }

            # Send notification to all mentioned users
            self.async_create_notification(
                client_id=client.id,
                recipient_type='individual',
                recipient_ids=valid_user_ids,
                title=title,
                body=body,
                data=data,
                notification_type=notification_type,
                enable_notification_push=enable_notification_push,
                enable_email_notification=enable_email_notification,
                update_badge=update_badge,
                scheduled_for=None
            )

            return True

        except Exception as e:
            logger.error(f"Error creating mention notification: {str(e)}")
            g.db_session.rollback()
            return False

    def new_feed_mention_notification(
        self,
        feed_id: str,
        poster_id: str,
        mentioned_user_ids: list
    ) -> bool:
        """Create a notification for users mentioned in feed posts."""
        try:
            if not mentioned_user_ids:
                return True
                
            # Get poster details
            poster = g.db_session.query(User).filter(
                User.id == poster_id,
                User.is_deleted == False
            ).first()
            
            if not poster:
                return False

            # Get client info
            client = g.db_session.query(Client).filter(
                Client.id_key == g.tenant_id
            ).first()

            if not client:
                return False

            # Filter out invalid user IDs and the poster themselves
            valid_mentioned_users = g.db_session.query(User).filter(
                User.id.in_(mentioned_user_ids),
                (
                    (User.settings.is_(None)) |  # No settings at all
                    (User.settings['notification_preference'].is_(None)) |  # No notification preferences
                    (User.settings['notification_preference']['metion_on_feed'].is_(None)) |  # Setting not specified
                    (User.settings['notification_preference']['metion_on_feed'].cast(Boolean).is_(True))  # Explicitly enabled
                )
            ).all()
            
            valid_user_ids = [user.id for user in valid_mentioned_users]
            
            if not valid_user_ids:
                return True

            # Create notification data
            update_badge = True
            enable_notification_push = True
            enable_email_notification = True
            notification_type = 'metion_on_feed'
            title = "You were mentioned in a post"
            body = f"{poster.preferred_name or (poster.first_name + ' ' + poster.last_name)} mentioned you in a post"
            data = {
                'type': 'feed',
                'action': f'/community?feed_id={feed_id}&client_id={client.id}#feed',
            }

            # Send notification to all mentioned users
            self.async_create_notification(
                client_id=client.id,
                recipient_type='individual',
                recipient_ids=valid_user_ids,
                title=title,
                body=body,
                data=data,
                notification_type=notification_type,
                enable_notification_push=enable_notification_push,
                enable_email_notification=enable_email_notification,
                update_badge=update_badge,
                scheduled_for=None
            )

            return True

        except Exception as e:
            logger.error(f"Error creating feed mention notification: {str(e)}")
            g.db_session.rollback()
            return False
