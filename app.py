from flask import Flask, render_template, Blueprint, g, request
from werkzeug.middleware.proxy_fix import ProxyFix
import os
import time
import logging

logger = logging.getLogger(__name__)

# Initialize Flask application
app = Flask(__name__, static_folder='static', template_folder='static')

# Load appropriate configuration
is_production = 'RUNNING_IN_PRODUCTION' in os.environ
app.config.update(IS_PRODUCTION=is_production)

if not is_production:
    print("Loading config.development and environment variables from .env file.")
    app.config.from_object('config.development')
else:
    print("Loading config.production.")
    app.config.from_object('config.production')

# Configure environment
environment = os.environ.get('ENVIRONMENT', 'dev')
app.config.update(ENVIRONMENT=environment)

# Configure proxy settings
app.wsgi_app = ProxyFix(
    app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1
)
app.config.update(
    PREFERRED_URL_SCHEME='https'
)

# Initialize configuration
# from config.init_logger import init_logger
# init_logger(app)

from config.init_cache import init_cache
cache = init_cache(app)

from config.init_compress import init_compress
init_compress(app)

from config.init_appinsights import init_app_insights
init_app_insights(app)

from config.init_apm import init_apm
init_apm(app)

from config.init_storage import init_storage
storage = init_storage(app)

from config.init_oauth import init_oauth
auth0 = init_oauth(app)

# Initialize database
from config.init_database import init_database, db, migrate
init_database(app)

## load api version number
from version import get_version
app.config.update(API_VERSION=get_version())
print(app.config.get('API_VERSION'))

## initialize the api route
from flask_restx import Api

## load the global api 
global_api = Api(
    version=app.config.get('API_VERSION', '1.0.0'),
    title='XAPA API Documentation',
    description='XAPA API Documentation'
)

## register global middleware
from api.common.middleware import add_middleware
add_middleware(app)

# Add application monitoring
from config.app_monitor import app_monitor

@app.before_request
def before_request():
    """Track requests and monitor application state"""
    app_monitor.record_request()
    g.start_time = time.time()

@app.after_request
def after_request(response):
    """Log slow requests and track response times"""
    if hasattr(g, 'start_time'):
        request_time = time.time() - g.start_time
        if request_time > 10.0:  # Log requests taking more than 10 seconds
            logger.warning(f"Slow request detected: {request.path} took {request_time:.2f}s")
    
    if response.status_code >= 500:
        app_monitor.record_error()
        
    return response

# Start monitoring
app_monitor.start_monitoring()

## load app apis
from api.app.quest import app_quest_api
from api.app.program import app_program_api
from api.app.xperience import app_xperience_api
from api.app.user import app_user_api
from api.app.xircle import app_xircle_api
from api.app.feed import app_feed_api
from api.app.global_settings import app_global_settings_api
from api.app.global_search import app_global_search_api
from api.app.stats import app_stats_api
from api.app.asset import app_asset_api
from api.app.leader_board import app_leader_board_api
from api.app.notification import app_notification_api
from api.app.home import app_home_api
from api.app.chest import app_chest_api
from api.app.addon import app_addon_api
from api.app.store import app_store_api
from api.app.chatbot import app_chatbot_api
from api.app.client_settings import app_client_settings_api

app_api_blueprint = Blueprint('app_api', __name__, url_prefix='/api/app')
app_api = Api(
    app_api_blueprint,
    doc='/document/',
    version=app.config.get('API_VERSION', '1.0.0'),
    title='XAPA APP API Documentation',
    description='XAPA APP API Documentation',
    authorizations={
        'bearer': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization',
            'description': 'Type in the textbox: Bearer <JWT>'
        }
    },
    endpoint='app_api'
)

app.register_blueprint(app_api_blueprint)
app_api.add_namespace(app_quest_api, path='/quest')
app_api.add_namespace(app_program_api, path='/program')
app_api.add_namespace(app_xperience_api, path='/xperience')
app_api.add_namespace(app_user_api, path='/user')
app_api.add_namespace(app_xircle_api, path='/xircle')
app_api.add_namespace(app_feed_api, path='/feed')
app_api.add_namespace(app_global_settings_api, path='/global_settings')
app_api.add_namespace(app_global_search_api, path='/global_search')
app_api.add_namespace(app_stats_api, path='/me')
app_api.add_namespace(app_asset_api, path='/asset')
app_api.add_namespace(app_leader_board_api, path='/leader_board')
app_api.add_namespace(app_notification_api, path='/notification')
app_api.add_namespace(app_home_api, path='/home')
app_api.add_namespace(app_chest_api, path='/chest')
app_api.add_namespace(app_addon_api, path='/addon')
app_api.add_namespace(app_store_api, path='/store')
app_api.add_namespace(app_chatbot_api, path='/chatbot')
app_api.add_namespace(app_client_settings_api, path='/client')


## load the api for the cms
from api.cms.clients import cms_clients_api
from api.cms.users import cms_users_api
from api.cms.attributes import cms_attributes_api
from api.cms.programs import cms_programs_api
from api.cms.xperiences import cms_xperiences_api
from api.cms.quests import cms_quests_api
from api.cms.nodes import cms_nodes_api
from api.cms.imports import cms_import_api
from api.cms.global_settings import cms_global_settings_api
from api.cms.asset import cms_asset_api
from api.cms.badges import cms_badges_api
from api.cms.xircles import cms_xircles_api
from api.cms.feeds import cms_feeds_api
from api.cms.notifications import cms_notifications_api
from api.cms.banners import cms_banners_api
from api.cms.packages import cms_packages_api
from api.cms.stores import cms_stores_api
from api.cms.analyses import cms_analyses_api
from api.cms.entitlement_groups import cms_entitlement_groups_api
from api.cms.xapa_day import cms_xapa_day_api

cms_api_blueprint = Blueprint('cms_api', __name__, url_prefix='/api/cms')
cms_api = Api(
    cms_api_blueprint,
    doc='/document/',
    version=app.config.get('API_VERSION', '1.0.0'),
    title='CMS API Documentation',
    description='CMS API Documentation',
    authorizations={
        'bearer': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization',
            'description': 'Type in the textbox: Bearer <JWT>'
        }
    },
    endpoint='cms_api'
)
app.register_blueprint(cms_api_blueprint)
cms_api.add_namespace(cms_clients_api, path='/clients')
cms_api.add_namespace(cms_users_api, path='/users')
cms_api.add_namespace(cms_attributes_api, path='/attributes')
cms_api.add_namespace(cms_programs_api, path='/programs')
cms_api.add_namespace(cms_xperiences_api, path='/xperiences')
cms_api.add_namespace(cms_quests_api, path='/quests')
cms_api.add_namespace(cms_nodes_api, path='/nodes')
cms_api.add_namespace(cms_import_api, path='/quests/import')
cms_api.add_namespace(cms_global_settings_api, path='/global_settings')
cms_api.add_namespace(cms_asset_api, path='/asset')
cms_api.add_namespace(cms_badges_api, path='/badges')
cms_api.add_namespace(cms_xircles_api, path='/xircles')
cms_api.add_namespace(cms_feeds_api, path='/feeds')
cms_api.add_namespace(cms_notifications_api, path='/notifications')
cms_api.add_namespace(cms_banners_api, path='/banners')
cms_api.add_namespace(cms_packages_api, path='/packages')
cms_api.add_namespace(cms_stores_api, path='/stores')
cms_api.add_namespace(cms_analyses_api, path='/analyses')
cms_api.add_namespace(cms_entitlement_groups_api, path='/entitlement_groups')
cms_api.add_namespace(cms_xapa_day_api, path='/xapa_days')

## load the account apis
from api.account.authentication import account_auth_api as account_auth_namespace
from api.account.admin import account_admin_api as account_admin_namespace
from api.account.user import account_user_api as account_user_namespace

account_api_blueprint = Blueprint('account_api', __name__, url_prefix='/api/account')
account_api = Api(
    account_api_blueprint,
    doc='/document/',
    version=app.config.get('API_VERSION', '1.0.0'),
    title='Account API Documentation',
    description='Account API Documentation',
    authorizations={
        'bearer': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization',
            'description': 'Type in the textbox: Bearer <JWT>'
        }
    },
    endpoint='account_api'
)
app.register_blueprint(account_api_blueprint)
account_api.add_namespace(account_auth_namespace, path='/')
account_api.add_namespace(account_admin_namespace, path='/admin')
account_api.add_namespace(account_user_namespace, path='/user')

## load the file apis
from api.file.file_handle import api_file as file_api_namespace

file_api_blueprint = Blueprint('file_api', __name__, url_prefix='/api/file')
file_api = Api(
    file_api_blueprint,
    doc='/document/',
    version=app.config.get('API_VERSION', '1.0.0'),
    title='Files API Documentation',
    description='Files API Documentation',
    authorizations={
        'bearer': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization',
            'description': 'Type in the textbox: Bearer <JWT>'
        }
    },
    endpoint='file_api'
)
app.register_blueprint(file_api_blueprint)
file_api.add_namespace(file_api_namespace, path='/')

## load the task apis
from api.task.task import task_execution_api
from api.task.scheduler import scheduler_execution_api
from api.task.notification import task_notification_api

task_api_blueprint = Blueprint('task_notification_api', __name__, url_prefix='/api/task')
task_api = Api(
    task_api_blueprint,
    doc='/document/',
    version=app.config.get('API_VERSION', '1.0.0'),
    title='Task API Documentation',
    description='Task API Documentation',
    authorizations={
        'bearer': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization',
            'description': 'Type in the textbox: Bearer <JWT>'
        }
    },
    endpoint='task_notification_api'
)
app.register_blueprint(task_api_blueprint)
task_api.add_namespace(task_execution_api, path='/')
task_api.add_namespace(task_notification_api, path='/notification')
task_api.add_namespace(scheduler_execution_api, path='/scheduler')

## load the test apis
from tests.test_quest_api import test_quest_api as test_quest_api_namespace
from tests.debug.user_data import debug_user_data_api as debug_user_data_namespace

test_api_blueprint = Blueprint('test_api', __name__, url_prefix='/api/test')
test_api = Api(
    test_api_blueprint,
    doc='/document/',
    version=app.config.get('API_VERSION', '1.0.0'),
    title='Test API Documentation',
    description='Test API Documentation',
    authorizations={
        'bearer': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization',
            'description': 'Type in the textbox: Bearer <JWT>'
        }
    },
    endpoint='test_api'
)
app.register_blueprint(test_api_blueprint)
test_api.add_namespace(test_quest_api_namespace, path='/quests')
test_api.add_namespace(debug_user_data_namespace, path='/debug/user_data')

@app.route('/', methods=['GET'], endpoint='index')
def index():
    ## load the index page
    return render_template('base.html')

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for monitoring"""
    try:
        from config.init_database import db
        import time
        
        start_time = time.time()
        
        # Test database connectivity
        db.session.execute('SELECT 1')
        db_time = time.time() - start_time
        
        # Check if response time is reasonable
        if db_time > 5.0:  # 5 seconds is too slow
            return {
                'status': 'unhealthy',
                'database': 'slow',
                'db_response_time': db_time,
                'timestamp': time.time()
            }, 503
            
        return {
            'status': 'healthy',
            'database': 'connected',
            'db_response_time': db_time,
            'timestamp': time.time()
        }, 200
        
    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }, 503

from sso import sso
app.register_blueprint(sso, url_prefix='/sso')

global_api.init_app(app)  # Bind Api instance to app

if __name__ == '__main__':
    app.run(debug=True)
