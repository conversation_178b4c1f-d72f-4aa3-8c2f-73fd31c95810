from flask import session
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from app import app

default_bind_key = "init"
USER_TENANT = "global"
MASTER_TENANT = "global"
CLIENT_TENANT = "global"

def get_db_session(tenant=None):
    if tenant is None:
        engine = create_engine(app.config.get('SQLALCHEMY_DATABASE_URI'))
    else:
        last_slash_index = app.config.get('SQLALCHEMY_DATABASE_URI').rfind("/")
        base_url = app.config.get('SQLALCHEMY_DATABASE_URI')[:last_slash_index + 1]
        engine = create_engine(base_url + tenant)

    Session = sessionmaker(bind=engine)
    db_session = Session()
    return db_session


def create_client_db(client_id):
    """Create a new database for a client"""
    last_slash_index = app.config.get('SQLALCHEMY_DATABASE_URI').rfind("/")
    base_url = app.config.get('SQLALCHEMY_DATABASE_URI')[:last_slash_index + 1]
    
    # Create a connection to the admin database
    admin_engine = create_engine(base_url + 'postgres')
    
    conn = admin_engine.connect()
    # Set isolation level to AUTOCOMMIT
    conn = conn.execution_options(isolation_level="AUTOCOMMIT")
    
    try:
        # Check if database exists using parameters
        result = conn.execute(text("SELECT 1 FROM pg_database WHERE datname = :db_name"), {"db_name": client_id})
        exists = result.scalar()
        
        if not exists:
            # For CREATE DATABASE, we can't use parameters, but our db_name is already sanitized
            conn.execute(text(f"CREATE DATABASE {client_id}"))
        
        # Close postgres connection before connecting to new database
        conn.close()
        admin_engine.dispose()
        
        # Create new engine for the client database
        client_engine = create_engine(base_url + client_id)
        
        # Initialize database schema
        from models import Base, ClientBase
        
        # Create all tables from both base models
        Base.metadata.create_all(client_engine)
        ClientBase.metadata.create_all(client_engine)
        
        # Create a session to initialize any required data
        Session = sessionmaker(bind=client_engine)
        session = Session()
        try:
            # Initialize any required default data here
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
            client_engine.dispose()
        
        return client_engine
        
    except Exception as e:
        if 'conn' in locals():
            conn.close()
        if 'admin_engine' in locals():
            admin_engine.dispose()
        raise e





from models import ClientBase
class Client(ClientBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



from models import UserBase
class User(UserBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



from models import MasterUserBase
class MasterUser(MasterUserBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserAssignmentBase
class UserAssignment(UserAssignmentBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserTokenBase
class UserToken(UserTokenBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



from models import UserLoginLogBase
class UserLoginLog(UserLoginLogBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import UserGraphTokenBase
class UserGraphToken(UserGraphTokenBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__

    
from models import AdminBase
class Admin(AdminBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import XircleBase
class Xircle(XircleBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import XircleMemberBase
class XircleMember(XircleMemberBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import FeedBase
class Feed(FeedBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import FeedCommentBase
class FeedComment(FeedCommentBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import FeedLikeBase
class FeedLike(FeedLikeBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



from models import FeedFlagBase
class FeedFlag(FeedFlagBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import FeedSeniorityBase
class FeedSeniority(FeedSeniorityBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__    



from models import UserStatsBase
class UserStats(UserStatsBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



from models import UserStreakBase
class UserStreak(UserStreakBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import UserCheckinBase
class UserCheckin(UserCheckinBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import UserXAPADayBase
class UserXAPADay(UserXAPADayBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import UserXPBase
class UserXP(UserXPBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import UserXPFacetBase
class UserXPFacet(UserXPFacetBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserCoinsBase
class UserCoins(UserCoinsBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserGemsBase
class UserGems(UserGemsBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import UserKeysBase
class UserKeys(UserKeysBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserSpendingBase
class UserSpending(UserSpendingBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import UserAssetBase
class UserAsset(UserAssetBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserBadgeBase
class UserBadge(UserBadgeBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import UserActivityBase
class UserActivity(UserActivityBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserProgramBase
class UserProgram(UserProgramBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



from models import UserXperienceBase
class UserXperience(UserXperienceBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserQuestBase
class UserQuest(UserQuestBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserNodeBase
class UserNode(UserNodeBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserNodeHistoryBase
class UserNodeHistory(UserNodeHistoryBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserChestBase
class UserChest(UserChestBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import ProgramBase
class Program(ProgramBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import ProgramDividerBase
class ProgramDivider(ProgramDividerBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import ProgramQuestAssociationBase
class ProgramQuestAssociation(ProgramQuestAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import XperienceBase
class Xperience(XperienceBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import XperienceQuestAssociationBase
class XperienceQuestAssociation(XperienceQuestAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import ProgramXperienceAssociationBase
class ProgramXperienceAssociation(ProgramXperienceAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import CategoryBase
class Category(CategoryBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import TagBase
class Tag(TagBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import FacetBase
class Facet(FacetBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import TargetCommunicationStyleBase
class TargetCommunicationStyle(TargetCommunicationStyleBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import LevelBase
class Level(LevelBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import ProgramCategoryAssociationBase
class ProgramCategoryAssociation(ProgramCategoryAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import ProgramTagAssociationBase
class ProgramTagAssociation(ProgramTagAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import ProgramFacetAssociationBase
class ProgramFacetAssociation(ProgramFacetAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    



from models import XperienceCategoryAssociationBase
class XperienceCategoryAssociation(XperienceCategoryAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import XperienceTagAssociationBase
class XperienceTagAssociation(XperienceTagAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import XperienceFacetAssociationBase
class XperienceFacetAssociation(XperienceFacetAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

    


from models import QuestBase
class Quest(QuestBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    



from models import QuestCategoryAssociationBase
class QuestCategoryAssociation(QuestCategoryAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



from models import QuestTagAssociationBase
class QuestTagAssociation(QuestTagAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import QuestFacetAssociationBase
class QuestFacetAssociation(QuestFacetAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    
    


from models import NodeBase
class Node(NodeBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import NodeOptionBase
class NodeOption(NodeOptionBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import NodeOptionMatchingBase
class NodeOptionMatching(NodeOptionMatchingBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



from models import NodeTranscriptBase
class NodeTranscript(NodeTranscriptBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import NodeBranchBase
class NodeBranch(NodeBranchBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import ChestBase
class Chest(ChestBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
  
from models import AssetBase
class Asset(AssetBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import ChestAssetAssociationBase
class ChestAssetAssociation(ChestAssetAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import BadgeBase
class Badge(BadgeBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import ProgramBadgeAssociationBase
class ProgramBadgeAssociation(ProgramBadgeAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import XperienceBadgeAssociationBase
class XperienceBadgeAssociation(XperienceBadgeAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import QuestBadgeAssociationBase
class QuestBadgeAssociation(QuestBadgeAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import PackageBase
class Package(PackageBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import PackageProgramAssociationBase
class PackageProgramAssociation(PackageProgramAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import PackageXperienceAssociationBase
class PackageXperienceAssociation(PackageXperienceAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import PackageQuestAssociationBase
class PackageQuestAssociation(PackageQuestAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import ClientPackageAssociationBase
class ClientPackageAssociation(ClientPackageAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import SSOConfigurationBase
class SSOConfiguration(SSOConfigurationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import ClientSSOAssociationBase
class ClientSSOAssociation(ClientSSOAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



from models import NotificationBase
class Notification(NotificationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__

from models import UserNotificationBase
class UserNotification(UserNotificationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)
    
    def __str__(self):
        return self.__bind_key__


from models import UserNotificationLogBase
class UserNotificationLog(UserNotificationLogBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)
    
    def __str__(self):
        return self.__bind_key__



from models import BannerBase
class Banner(BannerBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)
    
    def __str__(self):
        return self.__bind_key__
    


from models import CharacterBase
class Character(CharacterBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)
    
    def __str__(self):
        return self.__bind_key__
    

from models import TimezoneMappingBase
class TimezoneMapping(TimezoneMappingBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)
    
    def __str__(self):
        return self.__bind_key__
    

from models import ContentPublishLogBase
class ContentPublishLog(ContentPublishLogBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)
    
    def __str__(self):
        return self.__bind_key__


from models import NotificationTemplateBase
class NotificationTemplate(NotificationTemplateBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import FileBase
class File(FileBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserBoostBase
class UserBoost(UserBoostBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserPowerUpBase
class UserPowerUp(UserPowerUpBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import StoreBase
class Store(StoreBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__

from models import UserStoreBase
class UserStore(UserStoreBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
        
        

from models import TaskBase
class Task(TaskBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__ 


from models import TaskLogBase
class TaskLog(TaskLogBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__ 


from models import SchedulerBase
class Scheduler(SchedulerBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__ 


from models import SchedulerLogBase
class SchedulerLog(SchedulerLogBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return CLIENT_TENANT

    def __str__(self):
        return f"<SchedulerLog {self.id}>"



from models import ContentLogBase
class ContentLog(ContentLogBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__ 


from models import SettingBase
class Setting(SettingBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()

    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__ 
    

from models import UserBlockBase
class UserBlock(UserBlockBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import EntitlementGroupBase
class EntitlementGroup(EntitlementGroupBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import EntitlementGroupUserAssignmentBase
class EntitlementGroupUserAssignment(EntitlementGroupUserAssignmentBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import EntitlementGroupProgramAssignmentBase
class EntitlementGroupProgramAssignment(EntitlementGroupProgramAssignmentBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import EntitlementGroupXperienceAssignmentBase
class EntitlementGroupXperienceAssignment(EntitlementGroupXperienceAssignmentBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import UserLiveActivityBase
class UserLiveActivity(UserLiveActivityBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import PublishedQuestBase
class PublishedQuest(PublishedQuestBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import PublishedXperienceBase
class PublishedXperience(PublishedXperienceBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import PublishedProgramBase
class PublishedProgram(PublishedProgramBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import PublishedNodeBase
class PublishedNode(PublishedNodeBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    
from models import PublishedNodeOptionBase
class PublishedNodeOption(PublishedNodeOptionBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    
from models import PublishedNodeTranscriptBase
class PublishedNodeTranscript(PublishedNodeTranscriptBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import PublishedNodeBranchBase
class PublishedNodeBranch(PublishedNodeBranchBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    
from models import PublishedNodeOptionMatchingBase
class PublishedNodeOptionMatching(PublishedNodeOptionMatchingBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    
    
from models import XapaDayBase
class XapaDay(XapaDayBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



from models import ClientProgramCategoryAssociationBase
class ClientProgramCategoryAssociation(ClientProgramCategoryAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    


from models import ClientXperienceCategoryAssociationBase
class ClientXperienceCategoryAssociation(ClientXperienceCategoryAssociationBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__
    

from models import UserStyleBase
class UserStyle(UserStyleBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__


from models import UserStyleMappingBase
class UserStyleMapping(UserStyleMappingBase):
    def __init__(self, bind_key=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__bind_key__ = bind_key or self.get_bind_key()
    
    def get_bind_key(self):
        return session.get('tenant_id_key', default_bind_key)

    def __str__(self):
        return self.__bind_key__



def log_content_operation(mapper, connection, target, operation):
    from flask import g
    log_entry = ContentLog(
        content_id=target.id,
        content_type=target.__tablename__,
        user_id=g.user_id,  # Assuming user_id is available in the target
        operation=operation,  # or 'insert' based on the event
        status=target.status 
    )
    g.db_session.add(log_entry)
    g.db_session.commit()

def after_insert_listener(mapper, connection, target):
    print("insert")
    log_content_operation(mapper, connection, target, 'insert')

def after_update_listener(mapper, connection, target):
    log_content_operation(mapper, connection, target, 'update')

def after_delete_listener(mapper, connection, target):
    log_content_operation(mapper, connection, target, 'delete')


# List of models to attach listeners to
models_to_listen = [Quest, Xperience, Program]

# for model in models_to_listen:
#     event.listen(model, 'after_insert', after_insert_listener)
#     event.listen(model, 'after_update', after_update_listener)
#     event.listen(model, 'after_delete', after_delete_listener)


